import psutil
import platform
import socket
import threading
import time
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QTimer
from database import DatabaseManager

class SystemMonitor(QObject):
    # Signals for real-time updates
    system_data_updated = pyqtSignal(dict)
    network_data_updated = pyqtSignal(list)
    alert_generated = pyqtSignal(str, str, str)  # type, severity, message
    
    def __init__(self):
        super().__init__()
        self.db = DatabaseManager()
        self.monitoring = False
        self.monitor_thread = None
        
        # Thresholds for alerts
        self.cpu_threshold = 80.0
        self.memory_threshold = 85.0
        self.disk_threshold = 90.0
        
        # Previous network stats for calculating rates
        self.prev_network_stats = None
        
        # Timer for periodic updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.collect_system_data)
        
    def start_monitoring(self, interval=5):
        """Start system monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self.update_timer.start(interval * 1000)  # Convert to milliseconds
            self.monitor_thread = threading.Thread(target=self.continuous_monitoring, daemon=True)
            self.monitor_thread.start()
    
    def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring = False
        self.update_timer.stop()
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
    
    def collect_system_data(self):
        """Collect current system data"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # Network statistics
            network_stats = psutil.net_io_counters()
            network_sent = network_stats.bytes_sent
            network_received = network_stats.bytes_recv
            
            # Active processes
            active_processes = len(psutil.pids())
            
            # Create data dictionary
            system_data = {
                'timestamp': datetime.now(),
                'cpu_percent': cpu_percent,
                'memory_percent': memory_percent,
                'disk_percent': disk_percent,
                'network_sent': network_sent,
                'network_received': network_received,
                'active_processes': active_processes,
                'memory_total': memory.total,
                'memory_available': memory.available,
                'disk_total': disk.total,
                'disk_free': disk.free
            }
            
            # Store in database
            self.db.insert_system_monitoring(
                cpu_percent, memory_percent, disk_percent,
                network_sent, network_received, active_processes
            )
            
            # Check for alerts
            self.check_system_alerts(system_data)
            
            # Emit signal for UI update
            self.system_data_updated.emit(system_data)
            
            return system_data
            
        except Exception as e:
            print(f"Error collecting system data: {e}")
            return None
    
    def get_network_connections(self):
        """Get current network connections"""
        try:
            connections = []
            for conn in psutil.net_connections(kind='inet'):
                if conn.status == psutil.CONN_ESTABLISHED:
                    try:
                        process = psutil.Process(conn.pid) if conn.pid else None
                        process_name = process.name() if process else "Unknown"
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        process_name = "Unknown"
                    
                    connection_data = {
                        'local_ip': conn.laddr.ip if conn.laddr else "",
                        'local_port': conn.laddr.port if conn.laddr else 0,
                        'remote_ip': conn.raddr.ip if conn.raddr else "",
                        'remote_port': conn.raddr.port if conn.raddr else 0,
                        'protocol': 'TCP' if conn.type == socket.SOCK_STREAM else 'UDP',
                        'status': conn.status,
                        'process_name': process_name,
                        'pid': conn.pid
                    }
                    connections.append(connection_data)
                    
                    # Store in database
                    self.db.insert_network_monitoring(
                        connection_data['local_ip'],
                        connection_data['remote_ip'],
                        connection_data['local_port'],
                        connection_data['remote_port'],
                        connection_data['protocol'],
                        connection_data['status'],
                        connection_data['process_name']
                    )
            
            # Emit signal for UI update
            self.network_data_updated.emit(connections)
            return connections
            
        except Exception as e:
            print(f"Error getting network connections: {e}")
            return []
    
    def get_running_processes(self):
        """Get list of running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
                try:
                    process_info = proc.info
                    process_info['memory_mb'] = proc.memory_info().rss / 1024 / 1024
                    processes.append(process_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            return processes
            
        except Exception as e:
            print(f"Error getting processes: {e}")
            return []
    
    def check_system_alerts(self, system_data):
        """Check for system alerts based on thresholds"""
        alerts = []
        
        # CPU alert
        if system_data['cpu_percent'] > self.cpu_threshold:
            message = f"High CPU usage detected: {system_data['cpu_percent']:.1f}%"
            alerts.append(('CPU', 'HIGH', message))
            self.db.insert_security_alert('CPU', 'HIGH', message)
        
        # Memory alert
        if system_data['memory_percent'] > self.memory_threshold:
            message = f"High memory usage detected: {system_data['memory_percent']:.1f}%"
            alerts.append(('MEMORY', 'HIGH', message))
            self.db.insert_security_alert('MEMORY', 'HIGH', message)
        
        # Disk alert
        if system_data['disk_percent'] > self.disk_threshold:
            message = f"High disk usage detected: {system_data['disk_percent']:.1f}%"
            alerts.append(('DISK', 'CRITICAL', message))
            self.db.insert_security_alert('DISK', 'CRITICAL', message)
        
        # Emit alerts
        for alert_type, severity, message in alerts:
            self.alert_generated.emit(alert_type, severity, message)
    
    def continuous_monitoring(self):
        """Continuous monitoring in background thread"""
        while self.monitoring:
            try:
                # Collect network connections every 30 seconds
                self.get_network_connections()
                time.sleep(30)
            except Exception as e:
                print(f"Error in continuous monitoring: {e}")
                time.sleep(5)
    
    def get_system_info(self):
        """Get detailed system information"""
        try:
            info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'hostname': socket.gethostname(),
                'ip_address': socket.gethostbyname(socket.gethostname()),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()),
                'cpu_count': psutil.cpu_count(),
                'cpu_freq': psutil.cpu_freq().current if psutil.cpu_freq() else 0
            }
            return info
        except Exception as e:
            print(f"Error getting system info: {e}")
            return {}
    
    def get_disk_usage(self):
        """Get disk usage for all mounted drives"""
        try:
            disk_usage = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info = {
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100
                    }
                    disk_usage.append(disk_info)
                except PermissionError:
                    continue
            return disk_usage
        except Exception as e:
            print(f"Error getting disk usage: {e}")
            return []
    
    def set_thresholds(self, cpu_threshold=None, memory_threshold=None, disk_threshold=None):
        """Set alert thresholds"""
        if cpu_threshold is not None:
            self.cpu_threshold = cpu_threshold
        if memory_threshold is not None:
            self.memory_threshold = memory_threshold
        if disk_threshold is not None:
            self.disk_threshold = disk_threshold
