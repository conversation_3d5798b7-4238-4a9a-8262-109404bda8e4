# لوحة تحكم تعليمية (Educational Dashboard)

هذا المشروع عبارة عن تطبيق سطح مكتب تعليمي يشبه لوحة التحكم المرجعية. تم إنشاء هذا التطبيق لأغراض تعليمية فقط.

## المميزات

- واجهة مستخدم جذابة بتصميم داكن
- لوحة معلومات إحصائية تفاعلية
- عرض بيانات البلدان مع الأعلام والإحصاءات
- قسم محافظ العملات الرقمية
- قسم كلمات المرور والملفات
- بيانات محاكاة للأغراض التعليمية

## متطلبات النظام

- Python 3.6 أو أحدث
- PyQt5

## التثبيت

1. قم بتثبيت Python من [python.org](https://www.python.org/downloads/)
2. قم بتثبيت PyQt5 باستخدام pip:

```bash
pip install PyQt5
```

3. قم بتنزيل أو استنساخ هذا المشروع إلى جهاز الكمبيوتر الخاص بك

## كيفية التشغيل

1. انتقل إلى مجلد المشروع:

```bash
cd dashboard_app
```

2. قم بتشغيل التطبيق:

```bash
python src/main.py
```

## هيكل المشروع

```
dashboard_app/
├── assets/            # ملفات الأصول (الصور والأيقونات)
├── data/              # مجلد البيانات (سيتم إنشاؤه تلقائيًا)
├── src/               # كود المصدر
│   ├── main.py                    # نقطة الدخول الرئيسية للتطبيق
│   ├── dashboard_components.py    # مكونات لوحة المعلومات
│   ├── country_logs.py            # قسم سجلات البلدان
│   ├── wallet_sections.py         # قسم محافظ العملات الرقمية
│   └── password_sections.py       # قسم كلمات المرور والملفات
└── README.md          # ملف القراءة (هذا الملف)
```

## الاستخدام

1. عند تشغيل التطبيق، سترى واجهة لوحة التحكم الرئيسية
2. استخدم القائمة الجانبية للتنقل بين الأقسام المختلفة
3. انقر على زر "Simulate Data" لإنشاء بيانات محاكاة لأغراض العرض
4. استكشف علامات التبويب المختلفة لعرض المعلومات المختلفة

## إخلاء المسؤولية

هذا التطبيق مخصص للأغراض التعليمية فقط. لا يتم جمع أو تخزين أي بيانات حقيقية. جميع البيانات المعروضة هي بيانات محاكاة.

## الترخيص

حقوق النشر © 2025. هذا المشروع مخصص للأغراض التعليمية فقط.
