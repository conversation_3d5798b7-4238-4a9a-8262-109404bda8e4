import sys
import os
from PyQt5.QtWidgets import (QA<PERSON><PERSON>, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QTabWidget, QLabel, QStatusBar, QMessageBox, QSplashScreen)
from PyQt5.QtGui import QFont, QIcon, QPixmap
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal

# Import new real functionality modules
from database import DatabaseManager
from system_monitor import SystemMonitor
from password_manager import PasswordManagerWidget
from reports_generator import ReportsWidget
from network_monitor import NetworkMonitorWidget
from security_alerts import SecurityAlertsWidget

# Import existing modules (will be updated)
from dashboard_components import DashboardComponents
from country_logs import CountryLogsWidget

class DashboardApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Cybersecurity Monitoring System")
        self.setGeometry(100, 100, 1400, 900)
        self.setStyleSheet("background-color: #1E1E2E;")

        # Initialize database and system monitor
        self.db = DatabaseManager()
        self.system_monitor = SystemMonitor()

        # Initialize dashboard components (legacy)
        self.dashboard = DashboardComponents(self)

        # Set up the main layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)

        # Create sidebar and content area
        self.setup_sidebar()
        self.setup_content_area()

        # Add status bar
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("background-color: #1A1A28; color: #AAAAAA;")
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Cybersecurity Monitoring System - Real-time Protection Active", 5000)

        # Set up timer for periodic updates (every 5 seconds)
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(5000)

        # Connect system monitor signals
        self.system_monitor.system_data_updated.connect(self.on_system_data_updated)
        self.system_monitor.alert_generated.connect(self.on_alert_generated)

        # Start system monitoring
        self.system_monitor.start_monitoring()

        # Initial UI update
        self.update_ui()

    def setup_sidebar(self):
        # Create sidebar frame
        sidebar_frame = QWidget()
        sidebar_frame.setStyleSheet("background-color: #1A1A28;")
        sidebar_frame.setFixedWidth(200)
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)

        # Add logo at the top
        logo_label = QLabel("Dashboard")
        logo_label.setStyleSheet("color: #8E79E8; font-size: 18px; font-weight: bold;")
        logo_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(logo_label)

        # Add separator
        separator = QWidget()
        separator.setStyleSheet("background-color: #333344;")
        separator.setFixedHeight(1)
        sidebar_layout.addWidget(separator)

        # Add menu items
        menu_items = [
            {"name": "Dashboard", "action": self.show_dashboard},
            {"name": "System Monitor", "action": self.show_system_monitor},
            {"name": "Password Manager", "action": self.show_password_manager},
            {"name": "Network Monitor", "action": self.show_network_monitor},
            {"name": "Security Alerts", "action": self.show_security_alerts},
            {"name": "Reports", "action": self.show_reports},
            {"name": "Settings", "action": self.show_settings},
            {"name": "About", "action": self.show_about},
            {"name": "Exit", "action": self.close}
        ]

        for item in menu_items:
            menu_button = QWidget()
            menu_button.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    border-radius: 5px;
                }
                QWidget:hover {
                    background-color: #2D2D3F;
                }
            """)
            menu_button.setFixedHeight(40)

            button_layout = QHBoxLayout(menu_button)
            button_layout.setContentsMargins(10, 0, 10, 0)

            button_label = QLabel(item["name"])
            button_label.setStyleSheet("color: #CCCCCC; font-size: 14px;")
            button_layout.addWidget(button_label)

            # Connect click event
            menu_button.mousePressEvent = lambda event, action=item["action"]: action()

            sidebar_layout.addWidget(menu_button)

        # Add spacer at the bottom
        sidebar_layout.addStretch()

        # Add simulate data button (for educational purposes)
        simulate_button = QWidget()
        simulate_button.setStyleSheet("""
            QWidget {
                background-color: #2D2D3F;
                border-radius: 5px;
            }
            QWidget:hover {
                background-color: #3D3D4F;
            }
        """)
        simulate_button.setFixedHeight(40)

        simulate_layout = QHBoxLayout(simulate_button)
        simulate_layout.setContentsMargins(10, 0, 10, 0)

        simulate_label = QLabel("Simulate Data")
        simulate_label.setStyleSheet("color: #8E79E8; font-size: 14px;")
        simulate_layout.addWidget(simulate_label)

        # Connect click event
        simulate_button.mousePressEvent = lambda event: self.simulate_data()

        sidebar_layout.addWidget(simulate_button)

        # Add logo at the bottom
        bottom_logo = QLabel("ZERO TRACE")
        bottom_logo.setStyleSheet("color: #8E79E8; font-size: 14px; font-weight: bold;")
        bottom_logo.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(bottom_logo)

        # Add educational disclaimer
        disclaimer = QLabel("For Educational\nPurposes Only")
        disclaimer.setStyleSheet("color: #AAAAAA; font-size: 10px;")
        disclaimer.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(disclaimer)

        self.main_layout.addWidget(sidebar_frame)

    def setup_content_area(self):
        # Create content frame
        self.content_frame = QWidget()
        self.content_frame.setStyleSheet("background-color: #1E1E2E;")
        self.content_layout = QVBoxLayout(self.content_frame)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)

        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1E1E2E;
            }
            QTabBar::tab {
                background-color: #1A1A28;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #2D2D3F;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)

        # Create dashboard tab
        self.dashboard_tab = QWidget()
        dashboard_layout = QVBoxLayout(self.dashboard_tab)
        dashboard_layout.setContentsMargins(0, 10, 0, 0)

        # Add statistics section
        self.stats_layout = QHBoxLayout()
        self.stats_layout.setSpacing(15)

        # Create 4 stat panels
        self.stat_panels = {}
        stat_titles = ["Total log", "Last Week", "Last 30d", "Total pages"]
        stat_keys = ["total_logs", "last_week", "last_month", "total_pages"]
        stat_subtitles = ["During all this time", "During all Week", "During all Month", "During All Time"]

        for i in range(4):
            stat_panel = self.create_stat_panel(stat_titles[i], "0", stat_subtitles[i])
            self.stat_panels[stat_keys[i]] = stat_panel
            self.stats_layout.addWidget(stat_panel)

        dashboard_layout.addLayout(self.stats_layout)

        # Add country logs widget
        self.country_logs = CountryLogsWidget(self.dashboard)
        dashboard_layout.addWidget(self.country_logs)

        # Add legacy components for compatibility
        # self.wallet_sections = WalletSectionsWidget(self.dashboard)
        # dashboard_layout.addWidget(self.wallet_sections)

        # Add dashboard tab to tab widget
        self.tab_widget.addTab(self.dashboard_tab, "Dashboard")

        # Create System Monitor tab
        self.system_monitor_tab = QWidget()
        system_monitor_layout = QVBoxLayout(self.system_monitor_tab)
        system_monitor_layout.setContentsMargins(0, 10, 0, 0)

        # Add system monitor widget (placeholder for now)
        system_monitor_label = QLabel("Real-time System Monitoring")
        system_monitor_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        system_monitor_label.setAlignment(Qt.AlignCenter)
        system_monitor_layout.addWidget(system_monitor_label)

        # Add system monitor tab to tab widget
        self.tab_widget.addTab(self.system_monitor_tab, "System Monitor")

        # Create Password Manager tab
        self.password_manager_tab = QWidget()
        password_manager_layout = QVBoxLayout(self.password_manager_tab)
        password_manager_layout.setContentsMargins(0, 10, 0, 0)

        # Add password manager widget
        self.password_manager = PasswordManagerWidget(self)
        password_manager_layout.addWidget(self.password_manager)

        # Add password manager tab to tab widget
        self.tab_widget.addTab(self.password_manager_tab, "Password Manager")

        # Create Network Monitor tab
        self.network_monitor_tab = QWidget()
        network_monitor_layout = QVBoxLayout(self.network_monitor_tab)
        network_monitor_layout.setContentsMargins(0, 10, 0, 0)

        # Add network monitor widget
        self.network_monitor = NetworkMonitorWidget(self)
        network_monitor_layout.addWidget(self.network_monitor)

        # Add network monitor tab to tab widget
        self.tab_widget.addTab(self.network_monitor_tab, "Network Monitor")

        # Create Security Alerts tab
        self.security_alerts_tab = QWidget()
        security_alerts_layout = QVBoxLayout(self.security_alerts_tab)
        security_alerts_layout.setContentsMargins(0, 10, 0, 0)

        # Add security alerts widget
        self.security_alerts = SecurityAlertsWidget(self)
        security_alerts_layout.addWidget(self.security_alerts)

        # Add security alerts tab to tab widget
        self.tab_widget.addTab(self.security_alerts_tab, "Security Alerts")

        # Create Reports tab
        self.reports_tab = QWidget()
        reports_layout = QVBoxLayout(self.reports_tab)
        reports_layout.setContentsMargins(0, 10, 0, 0)

        # Add reports widget
        self.reports_widget = ReportsWidget(self)
        reports_layout.addWidget(self.reports_widget)

        # Add reports tab to tab widget
        self.tab_widget.addTab(self.reports_tab, "Reports")

        # Create Settings tab
        self.settings_tab = QWidget()
        settings_layout = QVBoxLayout(self.settings_tab)
        settings_layout.setContentsMargins(0, 10, 0, 0)

        # Add settings widget (placeholder for now)
        settings_label = QLabel("System Settings & Configuration")
        settings_label.setStyleSheet("color: #FFFFFF; font-size: 18px; font-weight: bold;")
        settings_label.setAlignment(Qt.AlignCenter)
        settings_layout.addWidget(settings_label)

        # Add settings tab to tab widget
        self.tab_widget.addTab(self.settings_tab, "Settings")

        # Add tab widget to content layout
        self.content_layout.addWidget(self.tab_widget)

        # Add system information at the bottom
        info_label = QLabel("Cybersecurity Monitoring System - Real-time Protection & Analysis")
        info_label.setStyleSheet("color: #8E79E8; font-size: 12px; font-weight: bold;")
        info_label.setAlignment(Qt.AlignCenter)
        self.content_layout.addWidget(info_label)

        self.main_layout.addWidget(self.content_frame)

    def create_stat_panel(self, title, value, subtitle):
        panel = QWidget()
        panel.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        panel.setMinimumHeight(100)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        title_label = QLabel(title)
        title_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")

        value_label = QLabel(value)
        value_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        panel.value_label = value_label  # Store reference for updates

        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("color: #AAAAAA; font-size: 12px;")

        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)

        return panel

    def update_ui(self):
        """Update all UI components with current data"""
        # Update statistics panels
        stats = self.dashboard.get_stats()
        for key, panel in self.stat_panels.items():
            panel.value_label.setText(str(stats[key]))

        # Update country logs widget
        if hasattr(self, 'country_logs'):
            self.country_logs.update_data()

    def simulate_data(self):
        """Generate real system data for demonstration"""
        # Collect real system data instead of simulating
        system_data = self.system_monitor.collect_system_data()
        if system_data:
            self.status_bar.showMessage("Real system data collected successfully", 3000)
        else:
            self.status_bar.showMessage("Error collecting system data", 3000)

    def on_system_data_updated(self, data):
        """Handle system data updates"""
        # Update UI with real system data
        if hasattr(self, 'stat_panels'):
            # Update CPU usage
            if 'cpu_percent' in data:
                # You can add CPU panel update here
                pass

    def on_alert_generated(self, alert_type, severity, message):
        """Handle security alerts"""
        self.status_bar.showMessage(f"ALERT: {message}", 10000)
        # You can add more alert handling here

    # Menu actions
    def show_dashboard(self):
        self.tab_widget.setCurrentIndex(0)
        self.status_bar.showMessage("Dashboard - System Overview", 3000)

    def show_system_monitor(self):
        self.tab_widget.setCurrentIndex(1)  # System Monitor tab
        self.status_bar.showMessage("System Monitor - Real-time Performance Monitoring", 3000)

    def show_password_manager(self):
        self.tab_widget.setCurrentIndex(2)  # Password Manager tab
        self.status_bar.showMessage("Password Manager - Secure Password Storage", 3000)

    def show_network_monitor(self):
        self.tab_widget.setCurrentIndex(3)  # Network Monitor tab
        self.status_bar.showMessage("Network Monitor - Connection & Traffic Analysis", 3000)

    def show_security_alerts(self):
        self.tab_widget.setCurrentIndex(4)  # Security Alerts tab
        self.status_bar.showMessage("Security Alerts - Threat Detection & Response", 3000)

    def show_reports(self):
        self.tab_widget.setCurrentIndex(5)  # Reports tab
        self.status_bar.showMessage("Reports - Analytics & Documentation", 3000)

    def show_settings(self):
        self.tab_widget.setCurrentIndex(6)  # Settings tab
        self.status_bar.showMessage("Settings - System Configuration", 3000)

    def show_about(self):
        about_text = """
        Cybersecurity Monitoring System v2.0

        A comprehensive real-time cybersecurity monitoring and management platform.

        Features:
        • Real-time system performance monitoring
        • Secure password management with encryption
        • Network traffic analysis and monitoring
        • Security threat detection and alerting
        • Comprehensive reporting and analytics
        • Advanced system configuration options

        Developed for professional cybersecurity monitoring and system administration.
        """
        from PyQt5.QtWidgets import QMessageBox
        QMessageBox.about(self, "About Cybersecurity Monitoring System", about_text)

    def closeEvent(self, event):
        """Handle application close event"""
        # Stop system monitoring
        if hasattr(self, 'system_monitor'):
            self.system_monitor.stop_monitoring()

        # Clean up database connections
        if hasattr(self, 'db'):
            # Perform any necessary cleanup
            pass

        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DashboardApp()
    window.show()
    sys.exit(app.exec_())
