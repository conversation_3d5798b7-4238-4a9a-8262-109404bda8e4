import sys
import os
from PyQt5.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QLabel, QStatusBar
from PyQt5.QtGui import QFont, QIcon
from PyQt5.QtCore import Qt, <PERSON><PERSON><PERSON>r

from dashboard_components import DashboardComponents
from country_logs import CountryLogsWidget
from wallet_sections import WalletSectionsWidget, CryptocurrencyMonitor
from password_sections import PasswordSectionsWidget
from stealer_settings import StealerSettingsWidget
from builder import BuilderWidget
from bulk_destroy import BulkDestroyWidget

class DashboardApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Dashboard Panel - Educational Demo")
        self.setGeometry(100, 100, 1200, 800)
        self.setStyleSheet("background-color: #1E1E2E;")
        
        # Initialize dashboard components
        self.dashboard = DashboardComponents(self)
        
        # Set up the main layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # Create sidebar and content area
        self.setup_sidebar()
        self.setup_content_area()
        
        # Add status bar
        self.status_bar = QStatusBar()
        self.status_bar.setStyleSheet("background-color: #1A1A28; color: #AAAAAA;")
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Educational Demo Application - For Learning Purposes Only", 5000)
        
        # Set up timer for periodic updates (every 5 seconds)
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_ui)
        self.update_timer.start(5000)
        
        # Initial UI update
        self.update_ui()
    
    def setup_sidebar(self):
        # Create sidebar frame
        sidebar_frame = QWidget()
        sidebar_frame.setStyleSheet("background-color: #1A1A28;")
        sidebar_frame.setFixedWidth(200)
        sidebar_layout = QVBoxLayout(sidebar_frame)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)
        
        # Add logo at the top
        logo_label = QLabel("Dashboard")
        logo_label.setStyleSheet("color: #8E79E8; font-size: 18px; font-weight: bold;")
        logo_label.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(logo_label)
        
        # Add separator
        separator = QWidget()
        separator.setStyleSheet("background-color: #333344;")
        separator.setFixedHeight(1)
        sidebar_layout.addWidget(separator)
        
        # Add menu items
        menu_items = [
            {"name": "Dashboard", "action": self.show_dashboard},
            {"name": "Logs", "action": self.show_logs},
            {"name": "Stealer Settings", "action": self.show_stealer_settings},
            {"name": "Builder", "action": self.show_builder},
            {"name": "Bulk Destroy", "action": self.show_bulk_destroy},
            {"name": "About", "action": self.show_about},
            {"name": "Exit Software", "action": self.close}
        ]
        
        for item in menu_items:
            menu_button = QWidget()
            menu_button.setStyleSheet("""
                QWidget {
                    background-color: transparent;
                    border-radius: 5px;
                }
                QWidget:hover {
                    background-color: #2D2D3F;
                }
            """)
            menu_button.setFixedHeight(40)
            
            button_layout = QHBoxLayout(menu_button)
            button_layout.setContentsMargins(10, 0, 10, 0)
            
            button_label = QLabel(item["name"])
            button_label.setStyleSheet("color: #CCCCCC; font-size: 14px;")
            button_layout.addWidget(button_label)
            
            # Connect click event
            menu_button.mousePressEvent = lambda event, action=item["action"]: action()
            
            sidebar_layout.addWidget(menu_button)
        
        # Add spacer at the bottom
        sidebar_layout.addStretch()
        
        # Add simulate data button (for educational purposes)
        simulate_button = QWidget()
        simulate_button.setStyleSheet("""
            QWidget {
                background-color: #2D2D3F;
                border-radius: 5px;
            }
            QWidget:hover {
                background-color: #3D3D4F;
            }
        """)
        simulate_button.setFixedHeight(40)
        
        simulate_layout = QHBoxLayout(simulate_button)
        simulate_layout.setContentsMargins(10, 0, 10, 0)
        
        simulate_label = QLabel("Simulate Data")
        simulate_label.setStyleSheet("color: #8E79E8; font-size: 14px;")
        simulate_layout.addWidget(simulate_label)
        
        # Connect click event
        simulate_button.mousePressEvent = lambda event: self.simulate_data()
        
        sidebar_layout.addWidget(simulate_button)
        
        # Add logo at the bottom
        bottom_logo = QLabel("ZERO TRACE")
        bottom_logo.setStyleSheet("color: #8E79E8; font-size: 14px; font-weight: bold;")
        bottom_logo.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(bottom_logo)
        
        # Add educational disclaimer
        disclaimer = QLabel("For Educational\nPurposes Only")
        disclaimer.setStyleSheet("color: #AAAAAA; font-size: 10px;")
        disclaimer.setAlignment(Qt.AlignCenter)
        sidebar_layout.addWidget(disclaimer)
        
        self.main_layout.addWidget(sidebar_frame)
    
    def setup_content_area(self):
        # Create content frame
        self.content_frame = QWidget()
        self.content_frame.setStyleSheet("background-color: #1E1E2E;")
        self.content_layout = QVBoxLayout(self.content_frame)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(20)
        
        # Create tab widget
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #1E1E2E;
            }
            QTabBar::tab {
                background-color: #1A1A28;
                color: #AAAAAA;
                padding: 8px 16px;
                margin-right: 4px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }
            QTabBar::tab:selected {
                background-color: #2D2D3F;
                color: #FFFFFF;
            }
            QTabBar::tab:hover:!selected {
                background-color: #252535;
            }
        """)
        
        # Create dashboard tab
        self.dashboard_tab = QWidget()
        dashboard_layout = QVBoxLayout(self.dashboard_tab)
        dashboard_layout.setContentsMargins(0, 10, 0, 0)
        
        # Add statistics section
        self.stats_layout = QHBoxLayout()
        self.stats_layout.setSpacing(15)
        
        # Create 4 stat panels
        self.stat_panels = {}
        stat_titles = ["Total log", "Last Week", "Last 30d", "Total pages"]
        stat_keys = ["total_logs", "last_week", "last_month", "total_pages"]
        stat_subtitles = ["During all this time", "During all Week", "During all Month", "During All Time"]
        
        for i in range(4):
            stat_panel = self.create_stat_panel(stat_titles[i], "0", stat_subtitles[i])
            self.stat_panels[stat_keys[i]] = stat_panel
            self.stats_layout.addWidget(stat_panel)
        
        dashboard_layout.addLayout(self.stats_layout)
        
        # Add country logs widget
        self.country_logs = CountryLogsWidget(self.dashboard)
        dashboard_layout.addWidget(self.country_logs)
        
        # Add wallet sections widget
        self.wallet_sections = WalletSectionsWidget(self.dashboard)
        dashboard_layout.addWidget(self.wallet_sections)
        
        # Add dashboard tab to tab widget
        self.tab_widget.addTab(self.dashboard_tab, "Dashboard")
        
        # Create wallet tab
        self.wallet_tab = QWidget()
        wallet_layout = QVBoxLayout(self.wallet_tab)
        wallet_layout.setContentsMargins(0, 10, 0, 0)
        
        # Add wallet sections and crypto monitor
        wallet_layout.addWidget(WalletSectionsWidget(self.dashboard))
        wallet_layout.addWidget(CryptocurrencyMonitor(self.dashboard))
        
        # Add wallet tab to tab widget
        self.tab_widget.addTab(self.wallet_tab, "Wallets")
        
        # Create password tab
        self.password_tab = QWidget()
        password_layout = QVBoxLayout(self.password_tab)
        password_layout.setContentsMargins(0, 10, 0, 0)
        
        # Add password sections widget
        self.password_sections = PasswordSectionsWidget(self.dashboard)
        password_layout.addWidget(self.password_sections)
        
        # Add password tab to tab widget
        self.tab_widget.addTab(self.password_tab, "Passwords")
        
        # Create stealer settings tab
        self.stealer_settings_tab = QWidget()
        stealer_settings_layout = QVBoxLayout(self.stealer_settings_tab)
        stealer_settings_layout.setContentsMargins(0, 10, 0, 0)
        
        # Add stealer settings widget
        self.stealer_settings = StealerSettingsWidget(self)
        stealer_settings_layout.addWidget(self.stealer_settings)
        
        # Add stealer settings tab to tab widget
        self.tab_widget.addTab(self.stealer_settings_tab, "Stealer Settings")
        
        # Create builder tab
        self.builder_tab = QWidget()
        builder_layout = QVBoxLayout(self.builder_tab)
        builder_layout.setContentsMargins(0, 10, 0, 0)
        
        # Add builder widget
        self.builder = BuilderWidget(self)
        builder_layout.addWidget(self.builder)
        
        # Add builder tab to tab widget
        self.tab_widget.addTab(self.builder_tab, "Builder")
        
        # Create bulk destroy tab
        self.bulk_destroy_tab = QWidget()
        bulk_destroy_layout = QVBoxLayout(self.bulk_destroy_tab)
        bulk_destroy_layout.setContentsMargins(0, 10, 0, 0)
        
        # Add bulk destroy widget
        self.bulk_destroy = BulkDestroyWidget(self)
        bulk_destroy_layout.addWidget(self.bulk_destroy)
        
        # Add bulk destroy tab to tab widget
        self.tab_widget.addTab(self.bulk_destroy_tab, "Bulk Destroy")
        
        # Add tab widget to content layout
        self.content_layout.addWidget(self.tab_widget)
        
        # Add educational disclaimer at the bottom
        disclaimer = QLabel("DISCLAIMER: This application is for educational purposes only. No actual harmful functionality is implemented.")
        disclaimer.setStyleSheet("color: #AAAAAA; font-size: 12px; font-style: italic;")
        disclaimer.setAlignment(Qt.AlignCenter)
        self.content_layout.addWidget(disclaimer)
        
        self.main_layout.addWidget(self.content_frame)
    
    def create_stat_panel(self, title, value, subtitle):
        panel = QWidget()
        panel.setStyleSheet("background-color: #1A1A28; border-radius: 5px;")
        panel.setMinimumHeight(100)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #AAAAAA; font-size: 14px;")
        
        value_label = QLabel(value)
        value_label.setStyleSheet("color: #FFFFFF; font-size: 24px; font-weight: bold;")
        panel.value_label = value_label  # Store reference for updates
        
        subtitle_label = QLabel(subtitle)
        subtitle_label.setStyleSheet("color: #AAAAAA; font-size: 12px;")
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addWidget(subtitle_label)
        
        return panel
    
    def update_ui(self):
        """Update all UI components with current data"""
        # Update statistics panels
        stats = self.dashboard.get_stats()
        for key, panel in self.stat_panels.items():
            panel.value_label.setText(str(stats[key]))
        
        # Update country logs widget
        if hasattr(self, 'country_logs'):
            self.country_logs.update_data()
        
        # Update wallet sections widget
        if hasattr(self, 'wallet_sections'):
            self.wallet_sections.update_data()
        
        # Update password sections widget
        if hasattr(self, 'password_sections'):
            self.password_sections.update_data()
    
    def simulate_data(self):
        """Simulate data for educational purposes"""
        self.dashboard.simulate_data()
        self.update_ui()
        self.status_bar.showMessage("Simulated data generated for educational purposes", 3000)
    
    # Menu actions
    def show_dashboard(self):
        self.tab_widget.setCurrentIndex(0)
    
    def show_logs(self):
        self.tab_widget.setCurrentIndex(0)
        self.status_bar.showMessage("Logs section selected", 3000)
    
    def show_stealer_settings(self):
        self.tab_widget.setCurrentIndex(3)  # Index of stealer settings tab
        self.status_bar.showMessage("Stealer Settings section selected (Educational Demo Only)", 3000)
    
    def show_builder(self):
        self.tab_widget.setCurrentIndex(4)  # Index of builder tab
        self.status_bar.showMessage("Builder section selected (Educational Demo Only)", 3000)
    
    def show_bulk_destroy(self):
        self.tab_widget.setCurrentIndex(5)  # Index of bulk destroy tab
        self.status_bar.showMessage("Bulk Destroy section selected (Educational Demo Only)", 3000)
    
    def show_about(self):
        self.status_bar.showMessage("This is an educational demonstration application only", 5000)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DashboardApp()
    window.show()
    sys.exit(app.exec_())
